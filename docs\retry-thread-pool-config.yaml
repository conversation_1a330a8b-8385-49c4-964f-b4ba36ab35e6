# 重试线程池配置示例
# 在application.yaml中添加以下配置

task:
  execution:
    pool:
      # 重试线程池配置
      retry:
        core-size: 5          # 核心线程数，控制并发数量避免大模型限流
        max-size: 10          # 最大线程数
        queue-capacity: 1000  # 队列容量，可以容纳大量重试任务
    retry:
      thread-name-prefix: retry-  # 线程名前缀
      pool:
        keep-alive: 60            # 线程存活时间（秒）

# 其他相关配置
logging:
  level:
    com.faw.work.ais.aic.service.impl.LlmRecordServiceImpl: INFO  # 重试日志级别

# 大模型相关配置（如果需要调整）
spring:
  ai:
    dashscope:
      api-key: ${DASHSCOPE_API_KEY}
      chat:
        options:
          temperature: 0.7
          max-tokens: 2000
