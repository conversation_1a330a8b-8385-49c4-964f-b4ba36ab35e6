package com.faw.work.ais.aic.controller;

import com.faw.work.ais.aic.common.base.AiResult;
import com.faw.work.ais.aic.model.request.AiRequest;
import com.faw.work.ais.aic.model.request.ProcessRequest;
import com.faw.work.ais.aic.model.request.RetryFailedMessagesRequest;
import com.faw.work.ais.aic.model.request.RetryRequest;
import com.faw.work.ais.aic.model.response.AiResponse;
import com.faw.work.ais.aic.model.response.MessageQueueCleanResponse;
import com.faw.work.ais.aic.model.response.RetryFailedMessagesResponse;
import com.faw.work.ais.aic.service.LlmRecordService;
import com.faw.work.ais.aic.service.LlmRecordServiceV2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/llm-record")
@Slf4j
@Tag(name = "LlmRecordController", description = "处理外部调用大模型请求")
public class LlmRecordController {
    @Autowired
    private LlmRecordService llmRecordService;

    @Autowired
    private LlmRecordServiceV2 llmRecordServiceV2;

    /**
     * 处理对话，包括情绪分析和标签提取
     *
     * @param request 包含文本和语音COS URL的请求
     * @return 处理结果
     */
    @Operation(summary = "处理对话", description = "处理对话，包括情绪分析和标签提取")
    @PostMapping("/process-conversation")
    public AiResult<String> processConversation(@Valid @RequestBody ProcessRequest request) {
        log.info("开始处理对话，请求ID: {}, 用户输入: {},  语音URL: {}", request.getRequestId(), request.getUserInput(), request.getAudioUrl());
        llmRecordService.processConversation(request);
        return AiResult.successMsg("成功接收消息,请关注回调消息");
    }

    /**
     * 重试失败的记录
     *
     * @param request 包含请求ID的请求
     * @return 处理结果
     */
    @Operation(summary = "重试失败记录", description = "根据请求ID重试所有失败的记录")
    @PostMapping("/retry-failed")
    public AiResult<String> retryFailedRecords(@Valid @RequestBody RetryRequest request) {
        log.info("开始重试失败记录，请求ID: {}", request.getRequestId());
        for (String requestId : request.getRequestId()) {
            llmRecordService.retryFailedRecords(requestId);
        }
        return AiResult.successMsg("重试成功");
    }

    /**
     * 清理已完成的消息队列数据
     *
     * @return 清理结果
     */
    @Operation(summary = "清理已完成的消息队列", description = "清理状态为3（已完成）的消息队列数据")
    @PostMapping("/clean-completed-messages")
    public AiResult<MessageQueueCleanResponse> cleanCompletedMessages() {
        log.info("开始清理已完成的消息队列数据");
        MessageQueueCleanResponse response = llmRecordService.cleanCompletedMessages();
        return AiResult.success(response);
    }

    @Operation(summary = "处理对话", description = "处理对话，包括情绪分析和标签提取")
    @PostMapping("/process-conversation-demo")
    public AiResult<String> processConversationDemo(@Valid @RequestBody ProcessRequest request) {
        log.info("开始处理对话，请求ID: {}, 用户输入: {},  语音URL: {}", request.getRequestId(), request.getUserInput(), request.getAudioUrl());
        llmRecordServiceV2.processConversationAbTest(request);
        return AiResult.successMsg("成功接收消息,请关注回调消息");
    }

    /**
     * 测试窗口接口 - 用于测试AI模型对话功能
     *
     * @param request 包含模型配置和用户输入的请求
     * @return AI模型响应结果
     */
    @Operation(summary = "AI测试窗口", description = "用于测试AI模型对话功能，支持模型配置和参数调整")
    @PostMapping("/test-chat")
    public AiResult<AiResponse> testChat(@Valid @RequestBody AiRequest request) {
        log.info("开始测试AI对话，模型: {}, 用户输入: {}", request.getModelName(), request.getUserInput());
        return AiResult.success(llmRecordServiceV2.testChat(request));
    }

    /**
     * 重试所有失败的消息队列
     *
     * @param request 重试请求参数
     * @return 重试结果
     */
    @Operation(summary = "重试失败的消息队列", description = "[author:10200571]")
    @PostMapping("/retry-failed-messages")
    public AiResult<RetryFailedMessagesResponse> retryFailedMessages(@Valid @RequestBody RetryFailedMessagesRequest request) {
        log.info("开始重试所有失败的消息队列，清理成功数据: {}", request.getCleanSuccessData());
        RetryFailedMessagesResponse response = llmRecordService.retryAllFailedMessages(request);
        return AiResult.success(response);
    }
}
