# 重试失败消息队列接口文档

## 接口概述

该接口用于重试所有状态为失败（status=10）的消息队列数据，根据消息内容查询对应的LLM记录并进行重新处理。

## 接口信息

- **接口路径**: `/llm-record/retry-failed-messages`
- **请求方法**: `POST`
- **Content-Type**: `application/json`

## 请求参数

### RetryFailedMessagesRequest

| 字段名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| cleanSuccessData | Boolean | 否 | true | 是否清理成功处理的数据 |

### 请求示例

```json
{
  "cleanSuccessData": true
}
```

## 响应参数

### RetryFailedMessagesResponse

| 字段名 | 类型 | 说明 |
|--------|------|------|
| totalFailedMessages | Integer | 总共找到的失败消息数量 |
| successRetryCount | Integer | 成功重试的消息数量 |
| failedRetryCount | Integer | 重试失败的消息数量 |
| skippedCount | Integer | 跳过的消息数量（已完成状态） |
| cleanedCount | Integer | 清理的数据数量 |
| successRequestIds | List<String> | 成功重试的请求ID列表 |
| failedRequestIds | List<String> | 重试失败的请求ID列表 |

### 响应示例

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalFailedMessages": 10,
    "successRetryCount": 8,
    "failedRetryCount": 1,
    "skippedCount": 1,
    "cleanedCount": 8,
    "successRequestIds": ["req-001", "req-002", "req-003"],
    "failedRequestIds": ["req-004"]
  },
  "traceId": "trace-12345",
  "timestamp": "2025-07-26T10:30:00"
}
```

## 处理逻辑

1. **查询失败消息**: 查询所有status为10（失败）的message_queue记录
2. **获取LLM记录**: 根据messageContent（对应requestId）查询llm_record表
3. **状态过滤**: 只处理状态为00（未处理）和10（失败）的llm_record，跳过02（已完成）状态
4. **重试处理**: 调用processSlice方法进行重试处理
5. **状态更新**: 更新消息队列状态
6. **数据清理**: 如果cleanSuccessData为true，清理成功处理的数据

## 状态码说明

### Message Queue状态
- 0: 未处理
- 1: 处理中  
- 2: 已处理
- 10: 失败

### LLM Record状态
- 00: 未处理
- 01: 进行中
- 02: 已完成
- 10: 处理失败

## 使用场景

1. **定期重试**: 可以定期调用此接口重试失败的消息
2. **手动修复**: 当发现有失败的消息时，可以手动触发重试
3. **系统恢复**: 系统故障恢复后，重试之前失败的消息

## 注意事项

1. 该接口会处理所有失败的消息队列，请谨慎使用
2. 重试过程中会调用大模型API，可能产生费用
3. 建议在低峰期执行重试操作
4. 如果设置cleanSuccessData为true，成功处理的数据会被清理，请确保不再需要这些数据
