package com.faw.work.ais.aic.service;

import com.faw.work.ais.aic.model.request.RetryFailedMessagesRequest;
import com.faw.work.ais.aic.model.response.RetryFailedMessagesResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * LlmRecord重试功能测试
 * <AUTHOR>
 */
@SpringBootTest
@Slf4j
public class LlmRecordRetryTest {

    @Autowired
    private LlmRecordService llmRecordService;

    /**
     * 测试重试失败的消息队列功能
     */
    @Test
    public void testRetryFailedMessages() {
        log.info("开始测试重试失败的消息队列功能");
        
        // 创建重试请求
        RetryFailedMessagesRequest request = new RetryFailedMessagesRequest();
        request.setCleanSuccessData(false); // 测试时不清理数据
        
        try {
            // 执行重试
            RetryFailedMessagesResponse response = llmRecordService.retryAllFailedMessages(request);
            
            // 输出结果
            log.info("重试结果:");
            log.info("总共找到失败消息: {}", response.getTotalFailedMessages());
            log.info("成功重试: {}", response.getSuccessRetryCount());
            log.info("重试失败: {}", response.getFailedRetryCount());
            log.info("跳过数量: {}", response.getSkippedCount());
            log.info("清理数量: {}", response.getCleanedCount());
            log.info("成功的请求ID: {}", response.getSuccessRequestIds());
            log.info("失败的请求ID: {}", response.getFailedRequestIds());
            
            // 基本断言
            assert response.getTotalFailedMessages() >= 0;
            assert response.getSuccessRetryCount() >= 0;
            assert response.getFailedRetryCount() >= 0;
            assert response.getSkippedCount() >= 0;
            
            log.info("重试失败消息队列功能测试完成");
            
        } catch (Exception e) {
            log.error("测试重试功能时发生异常", e);
            throw e;
        }
    }
}
