package com.faw.work.ais.aic.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 */
@Configuration
@EnableAsync
public class AsyncThreadPoolConfig {

    @Value("${task.execution.pool.faq-publish.core-size:50}")
    private int faqPublishCorePoolSize;
    @Value("${task.execution.pool.faq-publish.max-size:100}")
    private int faqPublishMaxPoolSize;
    @Value("${task.execution.pool.faq-publish.queue-capacity:500}")
    private int faqPublishQueueCapacity;
    @Value("${task.execution.faq-publish.thread-name-prefix:task-}")
    private String faqPublishNamePrefix;
    @Value("${task.execution.faq-publish.pool.keep-alive:10}")
    private int faqPublishKeepAliveSeconds;



    @Value("${task.execution.pool.dms-emotion.core-size:20}")
    private int dmsEmotionCorePoolSize;
    @Value("${task.execution.pool.dms-emotion.max-size:100}")
    private int dmsEmotionMaxPoolSize;
    @Value("${task.execution.pool.dms-emotion.queue-capacity:1000}")
    private int dmsEmotionQueueCapacity;
    @Value("${task.execution.dms-emotion.thread-name-prefix:task-}")
    private String dmsEmotionNamePrefix;
    @Value("${task.execution.dms-emotion.pool.keep-alive:10}")
    private int dmsEmotionKeepAliveSeconds;

    @Value("${task.execution.pool.retry.core-size:3}")
    private int retryCorePoolSize;
    @Value("${task.execution.pool.retry.max-size:10}")
    private int retryMaxPoolSize;
    @Value("${task.execution.pool.retry.queue-capacity:1000}")
    private int retryQueueCapacity;
    @Value("${task.execution.retry.thread-name-prefix:retry-}")
    private String retryNamePrefix;
    @Value("${task.execution.retry.pool.keep-alive:60}")
    private int retryKeepAliveSeconds;

    @Value("${task.execution.pool.async.core-size:50}")
    private int asyncCorePoolSize;
    @Value("${task.execution.pool.async.max-size:100}")
    private int asyncMaxPoolSize;
    @Value("${task.execution.pool.async.queue-capacity:500}")
    private int asyncQueueCapacity;
    @Value("${task.execution.async.thread-name-prefix:task-}")
    private String asyncNamePrefix;
    @Value("${task.execution.async.pool.keep-alive:10}")
    private int asyncKeepAliveSeconds;

    @Value("${task.execution.pool.content-summary.core-size:50}")
    private int contentSummaryCorePoolSize;
    @Value("${task.execution.pool.content-summary.max-size:100}")
    private int contentSummaryMaxPoolSize;
    @Value("${task.execution.pool.content-summary.queue-capacity:500}")
    private int contentSummaryQueueCapacity;
    @Value("${task.execution.content-summary.thread-name-prefix:task-}")
    private String contentSummaryNamePrefix;
    @Value("${task.execution.content-summary.pool.keep-alive:10}")
    private int contentSummaryKeepAliveSeconds;

    @Bean("faqPublishExecutor")
    public Executor faqPublishExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(faqPublishMaxPoolSize);
        //核心线程数
        executor.setCorePoolSize(faqPublishCorePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(faqPublishQueueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(faqPublishNamePrefix + "faq-publish-");
        //线程存活时间
        executor.setKeepAliveSeconds(faqPublishKeepAliveSeconds);
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }


    @Bean("dmsEmotionExecutor")
    public Executor dmsEmotionExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(dmsEmotionMaxPoolSize);
        //核心线程数
        executor.setCorePoolSize(dmsEmotionCorePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(dmsEmotionQueueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(dmsEmotionNamePrefix + "dms-emotion-");
        //线程存活时间
        executor.setKeepAliveSeconds(dmsEmotionKeepAliveSeconds);
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }

    @Bean("retryExecutor")
    public Executor retryExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(retryMaxPoolSize);
        //核心线程数
        executor.setCorePoolSize(retryCorePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(retryQueueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(retryNamePrefix + "retry-");
        //线程存活时间
        executor.setKeepAliveSeconds(retryKeepAliveSeconds);
        // 拒绝策略：使用CallerRunsPolicy，避免任务丢失
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }



    @Bean("asyncExecutor")
    public Executor asyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(asyncMaxPoolSize);
        //核心线程数
        executor.setCorePoolSize(asyncCorePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(asyncQueueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(asyncNamePrefix + "async-"); // Changed prefix
        //线程存活时间
        executor.setKeepAliveSeconds(asyncKeepAliveSeconds);
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }

    @Bean("contentSummaryExecutor")
    public Executor contentSummaryExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setMaxPoolSize(contentSummaryMaxPoolSize);
        executor.setCorePoolSize(contentSummaryCorePoolSize);
        executor.setQueueCapacity(contentSummaryQueueCapacity);
        executor.setThreadNamePrefix(contentSummaryNamePrefix + "content-summary-");
        executor.setKeepAliveSeconds(contentSummaryKeepAliveSeconds);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        executor.initialize();
        return executor;
    }

}