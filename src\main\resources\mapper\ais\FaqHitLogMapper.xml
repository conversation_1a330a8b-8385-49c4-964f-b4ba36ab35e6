<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.aic.mapper.faq.FaqHitLogMapper">

    <!-- 统计有效知识命中数 -->
    <select id="countHitEffectiveKnowledge" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT knowledge_id)
        FROM faq_hit_log
        WHERE robot_id = #{robotId}
        AND environment = #{env}
        AND is_effective = 1
        <if test="startTime != null and startTime != ''">
            AND created_at >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND created_at &lt;= #{endTime}
        </if>
    </select>

    <!-- 统计总会话量 -->
    <select id="countTotalChat" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT chat_id)
        FROM faq_hit_log
        WHERE robot_id = #{robotId}
        AND environment = #{env}
        AND is_hit = #{isHit}
        <if test="startTime != null and startTime != ''">
            AND created_at >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND created_at &lt;= #{endTime}
        </if>
    </select>

    <!-- 查询TOP10命中知识 -->
    <select id="findTop10HitKnowledge"
            resultType="com.faw.work.ais.aic.model.response.FaqReportResponse$FaqHitRankItem">
        SELECT
        fk.question AS knowledgeName,
        COUNT(DISTINCT fhl.id) AS hitCount
        FROM faq_hit_log fhl
        INNER JOIN
        <choose>
            <when test="env == 'prod'">
                faq_knowledge_prod fk ON fhl.knowledge_id = fk.id
                INNER JOIN faq_robot_knowledge_joins_prod rj ON fhl.knowledge_id = rj.knowledge_id
            </when>
            <otherwise>
                faq_knowledge fk ON fhl.knowledge_id = fk.id
                INNER JOIN faq_robot_knowledge_joins rj ON fhl.knowledge_id = rj.knowledge_id
            </otherwise>
        </choose>
        WHERE fhl.robot_id = #{robotId}
        AND fhl.environment = #{env}
        AND rj.source = 'original'  -- 只统计原始知识
        <if test="startTime != null and startTime != ''">
            AND fhl.created_at >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND fhl.created_at &lt;= #{endTime}
        </if>
        GROUP BY fhl.knowledge_id, fk.question
        ORDER BY hitCount DESC
        LIMIT 10
    </select>

    <!-- 查询时间范围内的命中知识ID -->
    <select id="findHitKnowledgeIdsInDateRange" resultType="java.lang.String">
        SELECT id
        FROM faq_hit_log
        WHERE robot_id = #{robotId}
        AND environment = #{env}
        <if test="startTime != null and startTime != ''">
            AND created_at >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND created_at &lt;= #{endTime}
        </if>
    </select>
    <select id="countHitEffectiveOriginalKnowledge" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT fhl.knowledge_id)
        FROM faq_hit_log fhl
        INNER JOIN
        <choose>
            <when test="env == 'prod'">
                faq_robot_knowledge_joins_prod rj ON fhl.knowledge_id = rj.knowledge_id
            </when>
            <otherwise>
                faq_robot_knowledge_joins rj ON fhl.knowledge_id = rj.knowledge_id
            </otherwise>
        </choose>
        WHERE fhl.robot_id = #{robotId}
        AND fhl.environment = #{env}
        AND fhl.is_effective = 1
        AND rj.source = 'original'  -- 只统计原始知识
        <if test="startTime != null and startTime != ''">
            AND fhl.created_at >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND fhl.created_at &lt;= #{endTime}
        </if>
    </select>
    <select id="findHitOriginalKnowledgeIdsInDateRange" resultType="java.lang.String">
        SELECT DISTINCT fhl.knowledge_id  <!-- 修复：查询knowledge_id而不是id -->
        FROM faq_hit_log fhl
        INNER JOIN
        <choose>
            <when test="env == 'prod'">
                faq_robot_knowledge_joins_prod rj ON fhl.knowledge_id = rj.knowledge_id
            </when>
            <otherwise>
                faq_robot_knowledge_joins rj ON fhl.knowledge_id = rj.knowledge_id
            </otherwise>
        </choose>
        WHERE fhl.robot_id = #{robotId}
        AND fhl.environment = #{env}
        AND rj.source = 'original'  -- 只查询原始知识的命中
        <if test="startTime != null and startTime != ''">
            AND fhl.created_at >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND fhl.created_at &lt;= #{endTime}
        </if>
    </select>
    <select id="countByKnowledgeId" resultType="java.lang.Long">
        SELECT COUNT(id)
        FROM faq_hit_log
        WHERE knowledge_id = #{id} and is_hit = '1'
    </select>

    <!-- 清理一个月前的FAQ命中日志数据 -->
    <delete id="cleanOldHitLogs">
        DELETE FROM faq_hit_log
        WHERE created_at &lt; DATE_SUB(NOW(), INTERVAL 1 MONTH)
    </delete>

</mapper>