# 连接池状态监控说明

## 监控功能

在重试方法中添加了连接池状态监控，用于开发阶段观察连接池的实时状态。

## 监控点

1. **重试开始**: 记录初始连接池状态
2. **查询完成**: 查询失败消息后的连接池状态  
3. **任务提交完成**: 所有异步任务提交后的状态
4. **任务执行完成**: 所有任务执行完成后的状态
5. **重试结束**: 最终连接池状态

## 日志格式

```
[连接池监控-{context}] 总连接数: {total}, 活跃连接: {active}, 空闲连接: {idle}, 等待线程: {waiting}
```

## 示例日志

```
2025-07-26 23:30:00.123 INFO [main] - [连接池监控-重试开始] 总连接数: 5, 活跃连接: 1, 空闲连接: 4, 等待线程: 0
2025-07-26 23:30:00.234 INFO [main] - [连接池监控-查询完成] 总连接数: 5, 活跃连接: 1, 空闲连接: 4, 等待线程: 0
2025-07-26 23:30:00.345 INFO [main] - [连接池监控-任务提交完成] 总连接数: 8, 活跃连接: 3, 空闲连接: 5, 等待线程: 0
2025-07-26 23:30:05.456 INFO [main] - [连接池监控-任务执行完成] 总连接数: 8, 活跃连接: 1, 空闲连接: 7, 等待线程: 0
2025-07-26 23:30:05.567 INFO [main] - [连接池监控-重试结束] 总连接数: 8, 活跃连接: 1, 空闲连接: 7, 等待线程: 0
```

## 关键指标说明

- **总连接数**: 连接池中的总连接数
- **活跃连接**: 正在使用的连接数
- **空闲连接**: 可用的空闲连接数
- **等待线程**: 等待获取连接的线程数

## 健康状态判断

✅ **健康状态**:
- 等待线程数 = 0
- 空闲连接数 > 0
- 活跃连接数 < 最大连接数

⚠️ **需要关注**:
- 等待线程数 > 0
- 空闲连接数 = 0
- 活跃连接数接近最大连接数

❌ **异常状态**:
- 等待线程数持续增长
- 连接超时错误
- 活跃连接数 = 最大连接数且等待线程数 > 0

## 生产环境注意事项

**重要**: 这些监控日志仅用于开发阶段调试，生产环境部署前请注释掉相关代码：

1. 注释掉 `logConnectionPoolStatus()` 方法调用
2. 或者通过配置开关控制是否启用监控
3. 避免在生产环境产生过多日志

## 移除监控代码

生产环境部署前，可以通过以下方式移除监控：

```java
// 方式1: 注释掉方法调用
// logConnectionPoolStatus("重试开始");

// 方式2: 添加配置开关
@Value("${debug.connection-pool.enabled:false}")
private boolean connectionPoolDebugEnabled;

private void logConnectionPoolStatus(String context) {
    if (!connectionPoolDebugEnabled) {
        return;
    }
    // ... 监控逻辑
}
```
