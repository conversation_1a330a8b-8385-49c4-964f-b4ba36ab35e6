package com.faw.work.ais.aic.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.faw.work.ais.aic.mapper.faq.FaqHitLogMapper;
import com.faw.work.ais.aic.model.domain.FaqHitLogPO;
import com.faw.work.ais.aic.model.request.FaqSearchByRobotRequest;
import com.faw.work.ais.aic.model.response.FaqKnowledgeResponse;
import com.faw.work.ais.aic.service.AsyncService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AsyncServiceImpl implements AsyncService {
    @Autowired
    private FaqHitLogMapper faqHitLogMapper;


    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void logFaqHitAsync(FaqSearchByRobotRequest request, List<FaqKnowledgeResponse> knowledgeList) {
        String sessionId = request.getSessionId();
        if (StringUtils.isBlank(sessionId)) {
            sessionId = IdWorker.get32UUID();
        }

        String chatId = request.getChatId();
        if (StringUtils.isBlank(chatId)) {
            chatId = IdWorker.get32UUID();
        }

        try {
            if (knowledgeList != null && !knowledgeList.isEmpty()) {
                LocalDateTime now = LocalDateTime.now();


                for (FaqKnowledgeResponse knowledge : knowledgeList) {

                    FaqHitLogPO hitLog = new FaqHitLogPO()
                            .setRobotId(request.getRobotId())
                            // 存的是原始知识id
                            .setKnowledgeId(knowledge.getKnowledgeId())
                            .setHitTime(now)
                            .setEnvironment(request.getEnv())
                            .setSessionId(sessionId)
                            .setChatId(chatId)
                            .setIsHit("1")
                            .setIsEffective("1");

                    faqHitLogMapper.insert(hitLog);
                }
            }else{
                FaqHitLogPO notHit = new FaqHitLogPO()
                        .setRobotId(request.getRobotId())
                        .setEnvironment(request.getEnv())
                        .setSessionId(sessionId)
                        .setChatId(chatId)
                        .setIsHit("0")
                        .setIsEffective("1");

                faqHitLogMapper.insert(notHit);
            }
        } catch (Exception e) {
            log.error("logFaqHitAsync error:{}", e.getMessage());
        }
    }
}