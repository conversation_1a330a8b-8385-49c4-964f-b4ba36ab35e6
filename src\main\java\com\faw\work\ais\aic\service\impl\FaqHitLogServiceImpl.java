package com.faw.work.ais.aic.service.impl;

import com.faw.work.ais.aic.mapper.faq.FaqHitLogMapper;
import com.faw.work.ais.aic.model.response.FaqHitLogCleanResponse;
import com.faw.work.ais.aic.service.FaqHitLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * FAQ命中日志Service实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FaqHitLogServiceImpl implements FaqHitLogService {

    @Autowired
    private FaqHitLogMapper faqHitLogMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FaqHitLogCleanResponse cleanOldHitLogs() {
        log.info("开始清理一个月前的FAQ命中日志数据");
        
        LocalDateTime cleanTime = LocalDateTime.now();
        int cleanCount = faqHitLogMapper.cleanOldHitLogs();
        
        log.info("FAQ命中日志清理完成，共清理{}条记录", cleanCount);
        
        return FaqHitLogCleanResponse.builder()
                .cleanCount(cleanCount)
                .cleanTime(cleanTime)
                .description("成功清理一个月前的FAQ命中日志数据")
                .build();
    }
}
